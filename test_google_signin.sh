#!/bin/bash

# 🧪 سكريبت اختبار Google Sign-In للـ Play Store

echo "🔧 بدء اختبار Google Sign-In للـ Play Store..."

# تنظيف المشروع
echo "🧹 تنظيف المشروع..."
flutter clean

# تحديث التبعيات
echo "📦 تحديث التبعيات..."
flutter pub get

# بناء APK للإنتاج
echo "🏗️ بناء APK للإنتاج..."
flutter build apk --release

# التحقق من نجاح البناء
if [ $? -eq 0 ]; then
    echo "✅ تم بناء APK بنجاح!"
    
    # عرض معلومات APK
    echo "📱 معلومات APK:"
    ls -lh build/app/outputs/flutter-apk/app-release.apk
    
    echo ""
    echo "🧪 خطوات الاختبار التالية:"
    echo "1. تثبيت APK: flutter install --release"
    echo "2. فتح التطبيق واختبار Google Sign-In"
    echo "3. مراقبة logs: flutter logs"
    echo ""
    echo "📋 معلومات مهمة:"
    echo "- Package Name: com.busaty.school"
    echo "- SHA1: A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E"
    echo "- Client ID: 545165014521-q7v03d8o8qk4ugvk5tslb922ccfjjin5.apps.googleusercontent.com"
    
else
    echo "❌ فشل في بناء APK!"
    echo "تحقق من الأخطاء أعلاه"
    exit 1
fi

echo ""
echo "🎯 إذا نجح Google Sign-In في هذا APK، فسيعمل في Play Store أيضاً!"
