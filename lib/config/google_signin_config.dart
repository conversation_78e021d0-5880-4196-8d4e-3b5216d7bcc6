import 'package:flutter/foundation.dart';

/// Configuration class for Google Sign-In Client IDs
/// All Client IDs are from test-5c820 Firebase project
class GoogleSignInConfig {
  // Client ID for Android Debug/Development (SHA-1: 8ac73c0cffc21c28038497fb8e33f37b1d55d523)
  static const String androidDebugClientId =
      '545165014521-670ko5rl2799jf1iltuk2t6874mov5l2.apps.googleusercontent.com';

  // Client ID for Android Release (SHA-1: a81efc57f5b0311996396040bdfea37ccd04cd8e)
  static const String androidReleaseClientId =
      '545165014521-m0doh33j49vm6dhjl4t1vhbudn8ciahf.apps.googleusercontent.com';

  // Client ID for Android Play Store (SHA-1: 3cfda58a3b31262bbdc1af26b85fc284da66b467)
  static const String androidPlayStoreClientId =
      '545165014521-q7v03d8o8qk4ugvk5tslb922ccfjjin5.apps.googleusercontent.com';

  // Client ID for iOS (from test-5c820 project)
  static const String iosClientId =
      '545165014521-6lga42ejvda9e36l607gkllobevvpjfn.apps.googleusercontent.com';

  // Client ID for Web (from test-5c820 project - update when web app is added)
  static const String webClientId =
      '545165014521-your-web-client-id.apps.googleusercontent.com';

  // SHA-1 Fingerprints for reference
  static const String debugSHA1 =
      '8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23';
  static const String releaseSHA1 =
      'A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E';
  static const String playStoreSHA1 =
      'A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E'; // Same as release - using upload keystore

  /// Get the appropriate client ID based on the current platform and build mode
  static String get currentPlatformClientId {
    if (kIsWeb) {
      return webClientId;
    }

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        // Use debug client ID for debug builds, release for release builds
        return kDebugMode ? androidDebugClientId : androidReleaseClientId;
      case TargetPlatform.iOS:
        return iosClientId;
      default:
        return androidDebugClientId; // fallback
    }
  }

  /// Get Android client ID based on build mode
  static String get androidClientId {
    return kDebugMode ? androidDebugClientId : androidReleaseClientId;
  }

  /// Get specific client ID for Play Store (for manual testing)
  static String get playStoreClientId {
    return androidPlayStoreClientId;
  }

  /// Get all Android client IDs (for reference)
  static List<String> get allAndroidClientIds {
    return [
      androidDebugClientId,
      androidReleaseClientId,
      androidPlayStoreClientId,
    ];
  }
}
